import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'dart:io';
import '../services/order_service.dart';

class AcceptedOrdersScreen extends StatefulWidget {
  final bool isDark;
  final VoidCallback onToggleDarkMode;

  const AcceptedOrdersScreen({
    Key? key,
    required this.isDark,
    required this.onToggleDarkMode,
  }) : super(key: key);

  @override
  _AcceptedOrdersScreenState createState() => _AcceptedOrdersScreenState();
}

class _AcceptedOrdersScreenState extends State<AcceptedOrdersScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  bool _isDarkMode = false;
  List<Map<String, dynamic>> _savedDrivers = [];
  List<Map<String, dynamic>> _acceptedOrders = [];
  bool _isLoading = true;
  String? _errorMessage;

  // البيانات الوهمية للعرض في حالة عدم وجود بيانات حقيقية
  final List<Map<String, dynamic>> _dummyAcceptedOrders = [
    {
      'id': 'ORD-001',
      'customerName': 'أحمد محمد',
      'customerPhone': '+966 50 123 4567',
      'address': 'شارع الملك فهد، الرياض',
      'medicines': [
        {'name': 'باراسيتامول 500 مجم', 'quantity': 2, 'price': 25.0},
        {'name': 'فيتامين د', 'quantity': 1, 'price': 28.75},
      ],
      'totalAmount': 53.75,
      'status': 'مقبول',
      'acceptedAt': '2024-01-15 10:30',
      'estimatedDelivery': '45 دقيقة',
      'driverId': null,
      'driverName': null,
      'driverPhone': null,
      'prescriptionImage': 'assets/images/prescription1.jpg', // صورة روشتة من المستخدم
    },
    {
      'id': 'ORD-002',
      'customerName': 'فاطمة علي',
      'customerPhone': '+966 55 987 6543',
      'address': 'حي النخيل، جدة',
      'medicines': [
        {'name': 'أموكسيسيلين 250 مجم', 'quantity': 1, 'price': 45.0},
        {'name': 'مرهم مضاد حيوي', 'quantity': 1, 'price': 15.5},
      ],
      'totalAmount': 60.5,
      'status': 'قيد التجهيز',
      'acceptedAt': '2024-01-15 11:15',
      'estimatedDelivery': '30 دقيقة',
      'driverId': null,
      'driverName': null,
      'driverPhone': null,
      'prescriptionImage': 'assets/images/prescription2.jpg', // صورة روشتة من المستخدم
    },
    {
      'id': 'ORD-003',
      'customerName': 'محمد السعد',
      'customerPhone': '+966 56 111 2222',
      'address': 'شارع التحلية، الخبر',
      'medicines': [
        {'name': 'أنسولين', 'quantity': 2, 'price': 120.0},
        {'name': 'شرائط قياس السكر', 'quantity': 1, 'price': 35.0},
      ],
      'totalAmount': 155.0,
      'status': 'جاهز للتسليم',
      'acceptedAt': '2024-01-15 09:45',
      'estimatedDelivery': '15 دقيقة',
      'driverId': 'DRV-001',
      'driverName': 'خالد أحمد',
      'driverPhone': '+966 50 555 1234',
      'prescriptionImage': null, // لا توجد صورة روشتة لهذا الطلب
    },
  ];

  @override
  void initState() {
    super.initState();
    _isDarkMode = widget.isDark;
    _loadDarkMode();
    _loadSavedDrivers();
    _loadAcceptedOrders(); // جلب الطلبات المقبولة
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1000),
    );
    _animationController.forward();
  }

  /// جلب الطلبات المقبولة من الخادم
  Future<void> _loadAcceptedOrders() async {
    if (mounted) {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });
    }

    try {
      // جلب الطلبات المؤكدة (confirmed) والطلبات في مراحل مختلفة
      final confirmedResult = await OrderService.getPharmacyOrders(
        page: 1,
        limit: 50,
        status: 'confirmed',
      );

      final preparingResult = await OrderService.getPharmacyOrders(
        page: 1,
        limit: 50,
        status: 'preparing',
      );

      final readyResult = await OrderService.getPharmacyOrders(
        page: 1,
        limit: 50,
        status: 'ready',
      );

      final deliveryResult = await OrderService.getPharmacyOrders(
        page: 1,
        limit: 50,
        status: 'out_for_delivery',
      );

      if (mounted) {
        List<Map<String, dynamic>> allOrders = [];

        if (confirmedResult['success'] == true) {
          allOrders.addAll(List<Map<String, dynamic>>.from(confirmedResult['data'] ?? []));
        }

        if (preparingResult['success'] == true) {
          allOrders.addAll(List<Map<String, dynamic>>.from(preparingResult['data'] ?? []));
        }

        if (readyResult['success'] == true) {
          allOrders.addAll(List<Map<String, dynamic>>.from(readyResult['data'] ?? []));
        }

        if (deliveryResult['success'] == true) {
          allOrders.addAll(List<Map<String, dynamic>>.from(deliveryResult['data'] ?? []));
        }

        setState(() {
          _acceptedOrders = allOrders;
          _isLoading = false;
        });

        print('📋 تم جلب ${allOrders.length} طلب مقبول');

        // إذا لم توجد طلبات، استخدم البيانات الوهمية للعرض
        if (allOrders.isEmpty) {
          setState(() {
            _acceptedOrders = List<Map<String, dynamic>>.from(_dummyAcceptedOrders);
          });
        }
      }
    } catch (e) {
      print('❌ خطأ في جلب الطلبات المقبولة: $e');
      if (mounted) {
        setState(() {
          _errorMessage = 'خطأ في جلب الطلبات: ${e.toString()}';
          _isLoading = false;
          // استخدام البيانات الوهمية في حالة الخطأ
          _acceptedOrders = List<Map<String, dynamic>>.from(_dummyAcceptedOrders);
        });
      }
    }
  }

  Future<void> _loadDarkMode() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _isDarkMode = prefs.getBool('pharmacy_dark_mode') ?? widget.isDark;
    });
  }

  Future<void> _toggleDarkMode() async {
    setState(() {
      _isDarkMode = !_isDarkMode;
    });
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('pharmacy_dark_mode', _isDarkMode);
    widget.onToggleDarkMode();
  }

  Future<void> _loadSavedDrivers() async {
    final prefs = await SharedPreferences.getInstance();
    final driversJson = prefs.getString('saved_drivers') ?? '[]';
    setState(() {
      _savedDrivers = List<Map<String, dynamic>>.from(json.decode(driversJson));
    });
  }

  Future<void> _saveDrivers() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('saved_drivers', json.encode(_savedDrivers));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final bgColor = _isDarkMode ? const Color(0xFF1B1B1B) : Colors.white;
    final textColor = _isDarkMode ? Colors.white : const Color(0xFF1B1B1B);
    const primaryColor = Color(0xFF00BF63);
    final cardColor = _isDarkMode
        ? const Color(0xFF2A2A2A)
        : const Color(0xFFF8F9FA);

    return Scaffold(
      backgroundColor: bgColor,
      appBar: AppBar(
        backgroundColor: bgColor,
        elevation: 0,
        title: Text(
          'الطلبات المقبولة',
          style: TextStyle(
            color: textColor,
            fontSize: 24,
            fontWeight: FontWeight.bold,
            fontFamily: 'Tajawal',
          ),
        ),
        actions: [
          Container(
            margin: const EdgeInsets.only(right: 8),
            decoration: BoxDecoration(
              color: _isDarkMode ? const Color(0xFF2A2A2A) : const Color(0xFFE8F5E8),
              borderRadius: BorderRadius.circular(12),
            ),
            child: IconButton(
              icon: const Icon(Icons.refresh),
              color: primaryColor,
              onPressed: _loadAcceptedOrders,
              tooltip: 'تحديث الطلبات',
            ),
          ),
          Container(
            margin: const EdgeInsets.only(right: 8),
            decoration: BoxDecoration(
              color: _isDarkMode ? const Color(0xFF2A2A2A) : const Color(0xFFE8F5E8),
              borderRadius: BorderRadius.circular(12),
            ),
            child: IconButton(
              icon: Icon(
                _isDarkMode ? Icons.light_mode : Icons.dark_mode,
                color: primaryColor,
              ),
              onPressed: _toggleDarkMode,
            ),
          ),
        ],
      ),
      body: _isLoading
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(color: primaryColor),
                  const SizedBox(height: 16),
                  Text(
                    'جاري تحميل الطلبات المقبولة...',
                    style: TextStyle(
                      color: textColor,
                      fontSize: 16,
                      fontFamily: 'Tajawal',
                    ),
                  ),
                ],
              ),
            )
          : _errorMessage != null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.error_outline,
                        color: Colors.red,
                        size: 64,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        _errorMessage!,
                        style: TextStyle(
                          color: textColor,
                          fontSize: 16,
                          fontFamily: 'Tajawal',
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _loadAcceptedOrders,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: primaryColor,
                          foregroundColor: Colors.white,
                        ),
                        child: const Text(
                          'إعادة المحاولة',
                          style: TextStyle(fontFamily: 'Tajawal'),
                        ),
                      ),
                    ],
                  ),
                )
              : Column(
                  children: [
                    // Statistics Cards
                    Container(
                      padding: const EdgeInsets.all(16),
                      child: Row(
                        children: [
                          Expanded(
                            child: _buildStatCard(
                              'إجمالي الطلبات',
                              _acceptedOrders.length.toString(),
                              Icons.assignment,
                              primaryColor,
                              cardColor,
                              textColor,
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: _buildStatCard(
                              'قيد التجهيز',
                              _acceptedOrders.where((o) => o['status'] == 'قيد التجهيز' || o['status'] == 'preparing').length.toString(),
                              Icons.hourglass_empty,
                    Colors.orange,
                    cardColor,
                    textColor,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    'جاهز للتسليم',
                    _acceptedOrders.where((o) => o['status'] == 'جاهز للتسليم').length.toString(),
                    Icons.check_circle,
                    Colors.green,
                    cardColor,
                    textColor,
                  ),
                ),
              ],
            ),
          ),
          // Orders List
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: _acceptedOrders.length,
              itemBuilder: (context, index) {
                final order = _acceptedOrders[index];
                return _buildOrderCard(order, cardColor, textColor, primaryColor);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color, Color cardColor, Color textColor) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: cardColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              color: textColor,
              fontSize: 18,
              fontWeight: FontWeight.bold,
              fontFamily: 'Tajawal',
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              color: textColor.withValues(alpha: 0.7),
              fontSize: 10,
              fontFamily: 'Tajawal',
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildOrderCard(Map<String, dynamic> order, Color cardColor, Color textColor, Color primaryColor) {
    final statusColor = _getStatusColor(order['status']);
    final canAssignDriver = order['status'] == 'جاهز للتسليم' && order['driverId'] == null;
    final hasDriver = order['driverId'] != null;

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: cardColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Order Header
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'طلب رقم: ${order['orderNumber'] ?? order['id'] ?? 'غير محدد'}',
                      style: TextStyle(
                        color: textColor,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        fontFamily: 'Tajawal',
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      order['user']?['name'] ?? order['customerName'] ?? 'غير محدد',
                      style: TextStyle(
                        color: primaryColor,
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        fontFamily: 'Tajawal',
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: statusColor.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  _getStatusDisplayText(order['status'] ?? ''),
                  style: TextStyle(
                    color: statusColor,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    fontFamily: 'Tajawal',
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          
          // Customer Info
          _buildInfoRow('العميل', order['user']?['name'] ?? order['customerName'] ?? 'غير محدد', textColor),
          _buildInfoRow('الهاتف', order['user']?['phone'] ?? order['customerPhone'] ?? 'غير محدد', textColor),
          _buildInfoRow('الدواء المطلوب', order['medicineRequest']?['name'] ?? 'غير محدد', textColor),
          _buildInfoRow('الكمية', '${order['medicineRequest']?['quantity'] ?? 'غير محدد'}', textColor),
          if (order['pricing']?['total'] != null)
            _buildInfoRow('المبلغ الإجمالي', '${order['pricing']['total']} ج.م', textColor),
          if (order['createdAt'] != null)
            _buildInfoRow('تاريخ الطلب', _formatDate(order['createdAt']), textColor),

          // Prescription Image Section
          if (order['medicineRequest']?['prescriptionImageUrl'] != null || order['prescriptionImage'] != null) ...[
            const SizedBox(height: 12),
            Row(
              children: [
                Text(
                  'صورة الروشتة المرفقة:',
                  style: TextStyle(
                    color: textColor,
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    fontFamily: 'Tajawal',
                  ),
                ),
                const Spacer(),
                ElevatedButton.icon(
                  onPressed: () => _viewPrescriptionImage(
                    order['medicineRequest']?['prescriptionImageUrl'] ?? order['prescriptionImage'],
                    cardColor,
                    textColor
                  ),
                  icon: const Icon(Icons.visibility, size: 16),
                  label: const Text('عرض الروشتة', style: TextStyle(fontFamily: 'Tajawal', fontSize: 12)),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: primaryColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ],
            ),
          ],

          // Prescription Image Preview (if exists)
          if (order['prescriptionImage'] != null) ...[
            const SizedBox(height: 8),
            Container(
              height: 150,
              width: double.infinity,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: primaryColor.withValues(alpha: 0.3)),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: order['prescriptionImage'].toString().startsWith('assets/')
                    ? Image.asset(
                        order['prescriptionImage'],
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            color: Colors.grey[300],
                            child: const Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(Icons.image, color: Colors.grey, size: 48),
                                  SizedBox(height: 8),
                                  Text(
                                    'صورة الروشتة',
                                    style: TextStyle(
                                      color: Colors.grey,
                                      fontFamily: 'Tajawal',
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          );
                        },
                      )
                    : Image.file(
                        File(order['prescriptionImage']),
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            color: Colors.grey[300],
                            child: const Center(
                              child: Icon(Icons.error, color: Colors.red),
                            ),
                          );
                        },
                      ),
              ),
            ),
          ],

          // Driver Info (if assigned)
          if (hasDriver) ...[
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: primaryColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'معلومات السائق:',
                    style: TextStyle(
                      color: primaryColor,
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      fontFamily: 'Tajawal',
                    ),
                  ),
                  const SizedBox(height: 4),
                  _buildInfoRow('الاسم', order['driverName'], textColor),
                  _buildInfoRow('الهاتف', order['driverPhone'], textColor),
                ],
              ),
            ),
          ],
          
          const SizedBox(height: 12),
          
          // Medicines List
          Text(
            'الأدوية المطلوبة:',
            style: TextStyle(
              color: textColor,
              fontSize: 14,
              fontWeight: FontWeight.bold,
              fontFamily: 'Tajawal',
            ),
          ),
          const SizedBox(height: 8),
          ...order['medicines'].map<Widget>((medicine) => Container(
            margin: const EdgeInsets.only(bottom: 4),
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: _isDarkMode ? const Color(0xFF3A3A3A) : Colors.grey[100],
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    medicine['name'],
                    style: TextStyle(
                      color: textColor,
                      fontSize: 12,
                      fontFamily: 'Tajawal',
                    ),
                  ),
                ),
                Text(
                  'الكمية: ${medicine['quantity']}',
                  style: TextStyle(
                    color: textColor.withValues(alpha: 0.7),
                    fontSize: 12,
                    fontFamily: 'Tajawal',
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  '${medicine['price']} ج.م',
                  style: TextStyle(
                    color: primaryColor,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    fontFamily: 'Tajawal',
                  ),
                ),
              ],
            ),
          )).toList(),
          
          const SizedBox(height: 16),
          
          // Action Buttons
          Row(
            children: [
              if (order['status'] == 'مقبول')
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _updateOrderStatus(order, 'قيد التجهيز'),
                    icon: const Icon(Icons.hourglass_empty, size: 16),
                    label: const Text('بدء التجهيز', style: TextStyle(fontFamily: 'Tajawal')),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                ),
              if (order['status'] == 'قيد التجهيز')
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _updateOrderStatus(order, 'جاهز للتسليم'),
                    icon: const Icon(Icons.check_circle, size: 16),
                    label: const Text('جاهز للتسليم', style: TextStyle(fontFamily: 'Tajawal')),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                ),
              if (canAssignDriver) ...[
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _showDriverAssignmentDialog(order, cardColor, textColor, primaryColor),
                    icon: const Icon(Icons.delivery_dining, size: 16),
                    label: const Text('تعيين سائق', style: TextStyle(fontFamily: 'Tajawal')),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: primaryColor,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                ),
              ],
              if (hasDriver && order['status'] == 'جاهز للتسليم') ...[
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _updateOrderStatus(order, 'تم التسليم'),
                    icon: const Icon(Icons.done_all, size: 16),
                    label: const Text('تم التسليم', style: TextStyle(fontFamily: 'Tajawal')),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: primaryColor,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, Color textColor) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: TextStyle(
                color: textColor.withValues(alpha: 0.7),
                fontSize: 12,
                fontFamily: 'Tajawal',
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                color: textColor,
                fontSize: 12,
                fontWeight: FontWeight.bold,
                fontFamily: 'Tajawal',
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'مقبول':
      case 'confirmed':
        return Colors.blue;
      case 'قيد التجهيز':
      case 'preparing':
        return Colors.orange;
      case 'جاهز للتسليم':
      case 'ready':
        return Colors.green;
      case 'تم التسليم':
      case 'delivered':
        return const Color(0xFF00BF63);
      case 'out_for_delivery':
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }

  String _getStatusDisplayText(String status) {
    switch (status) {
      case 'confirmed':
        return 'مقبول';
      case 'preparing':
        return 'قيد التجهيز';
      case 'ready':
        return 'جاهز للتسليم';
      case 'out_for_delivery':
        return 'في الطريق';
      case 'delivered':
        return 'تم التسليم';
      default:
        return status;
    }
  }

  String _formatDate(String dateString) {
    try {
      final date = DateTime.parse(dateString);
      return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
    } catch (e) {
      return dateString;
    }
  }

  Future<void> _updateOrderStatus(Map<String, dynamic> order, String newStatus) async {
    try {
      // عرض مؤشر التحميل
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          backgroundColor: _isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const CircularProgressIndicator(color: Color(0xFF00BF63)),
              const SizedBox(height: 20),
              Text(
                'جاري تحديث حالة الطلب...',
                style: TextStyle(
                  color: _isDarkMode ? Colors.white : const Color(0xFF121212),
                  fontFamily: 'Tajawal',
                ),
              ),
            ],
          ),
        ),
      );

      // استدعاء API لتحديث حالة الطلب
      final result = await OrderService.updateOrderStatus(
        orderId: order['_id'] ?? order['id'] ?? '',
        status: newStatus,
      );

      if (!mounted) return;

      Navigator.pop(context); // إغلاق مؤشر التحميل

      if (result['success'] == true) {
        setState(() {
          order['status'] = newStatus;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'تم تحديث حالة الطلب إلى: ${_getStatusDisplayText(newStatus)}',
              style: const TextStyle(fontFamily: 'Tajawal'),
            ),
            backgroundColor: const Color(0xFF00BF63),
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              result['message'] ?? 'فشل في تحديث حالة الطلب',
              style: const TextStyle(fontFamily: 'Tajawal'),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        Navigator.pop(context); // إغلاق مؤشر التحميل
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'خطأ في تحديث حالة الطلب: ${e.toString()}',
              style: const TextStyle(fontFamily: 'Tajawal'),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showDriverAssignmentDialog(Map<String, dynamic> order, Color cardColor, Color textColor, Color primaryColor) {
    final nameController = TextEditingController();
    final phoneController = TextEditingController();
    String? selectedDriverId;
    bool isNewDriver = false;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          backgroundColor: cardColor,
          title: Text(
            'تعيين سائق للطلب ${order['id']}',
            style: TextStyle(
              color: textColor,
              fontFamily: 'Tajawal',
              fontSize: 18,
            ),
          ),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Existing Drivers Dropdown
                if (_savedDrivers.isNotEmpty) ...[
                  Text(
                    'اختر من السائقين المحفوظين:',
                    style: TextStyle(
                      color: textColor,
                      fontFamily: 'Tajawal',
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.symmetric(horizontal: 12),
                    decoration: BoxDecoration(
                      border: Border.all(color: primaryColor),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: DropdownButtonHideUnderline(
                      child: DropdownButton<String>(
                        value: selectedDriverId,
                        hint: Text(
                          'اختر سائق',
                          style: TextStyle(
                            color: textColor.withValues(alpha: 0.7),
                            fontFamily: 'Tajawal',
                          ),
                        ),
                        dropdownColor: cardColor,
                        items: [
                          DropdownMenuItem<String>(
                            value: null,
                            child: Text(
                              'سائق جديد',
                              style: TextStyle(
                                color: textColor,
                                fontFamily: 'Tajawal',
                              ),
                            ),
                          ),
                          ..._savedDrivers.map((driver) => DropdownMenuItem<String>(
                            value: driver['id'],
                            child: Text(
                              '${driver['name']} - ${driver['phone']}',
                              style: TextStyle(
                                color: textColor,
                                fontFamily: 'Tajawal',
                              ),
                            ),
                          )).toList(),
                        ],
                        onChanged: (value) {
                          setDialogState(() {
                            selectedDriverId = value;
                            isNewDriver = value == null;
                            if (value != null) {
                              final driver = _savedDrivers.firstWhere((d) => d['id'] == value);
                              nameController.text = driver['name'];
                              phoneController.text = driver['phone'];
                            } else {
                              nameController.clear();
                              phoneController.clear();
                            }
                          });
                        },
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                ],

                // Driver Name Field
                Text(
                  'اسم السائق:',
                  style: TextStyle(
                    color: textColor,
                    fontFamily: 'Tajawal',
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                TextField(
                  controller: nameController,
                  style: TextStyle(color: textColor, fontFamily: 'Tajawal'),
                  decoration: InputDecoration(
                    hintText: 'أدخل اسم السائق',
                    hintStyle: TextStyle(
                      color: textColor.withValues(alpha: 0.5),
                      fontFamily: 'Tajawal',
                    ),
                    border: OutlineInputBorder(
                      borderSide: BorderSide(color: primaryColor),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: primaryColor, width: 2),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: primaryColor.withValues(alpha: 0.5)),
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
                const SizedBox(height: 16),

                // Driver Phone Field
                Text(
                  'رقم هاتف السائق:',
                  style: TextStyle(
                    color: textColor,
                    fontFamily: 'Tajawal',
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                TextField(
                  controller: phoneController,
                  keyboardType: TextInputType.phone,
                  style: TextStyle(color: textColor, fontFamily: 'Tajawal'),
                  decoration: InputDecoration(
                    hintText: 'أدخل رقم الهاتف',
                    hintStyle: TextStyle(
                      color: textColor.withValues(alpha: 0.5),
                      fontFamily: 'Tajawal',
                    ),
                    border: OutlineInputBorder(
                      borderSide: BorderSide(color: primaryColor),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: primaryColor, width: 2),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: primaryColor.withValues(alpha: 0.5)),
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(
                'إلغاء',
                style: TextStyle(
                  color: textColor.withValues(alpha: 0.7),
                  fontFamily: 'Tajawal',
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                if (nameController.text.trim().isEmpty || phoneController.text.trim().isEmpty) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text(
                        'يرجى إدخال اسم السائق ورقم الهاتف',
                        style: TextStyle(fontFamily: 'Tajawal'),
                      ),
                      backgroundColor: Colors.red,
                    ),
                  );
                  return;
                }

                _assignDriverToOrder(
                  order,
                  nameController.text.trim(),
                  phoneController.text.trim(),
                  isNewDriver,
                  cardColor,
                  textColor,
                  primaryColor,
                );
                Navigator.pop(context);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: primaryColor,
                foregroundColor: Colors.white,
              ),
              child: const Text(
                'تعيين السائق',
                style: TextStyle(fontFamily: 'Tajawal'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _assignDriverToOrder(
    Map<String, dynamic> order,
    String driverName,
    String driverPhone,
    bool isNewDriver,
    Color cardColor,
    Color textColor,
    Color primaryColor,
  ) {
    if (isNewDriver) {
      // Show dialog to ask if user wants to save the new driver
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          backgroundColor: cardColor,
          title: Text(
            'حفظ السائق',
            style: TextStyle(
              color: textColor,
              fontFamily: 'Tajawal',
            ),
          ),
          content: Text(
            'هل تريد حفظ معلومات السائق "$driverName" في سجل السائقين؟',
            style: TextStyle(
              color: textColor,
              fontFamily: 'Tajawal',
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                _completeDriverAssignment(order, driverName, driverPhone, false);
              },
              child: Text(
                'لا',
                style: TextStyle(
                  color: textColor.withValues(alpha: 0.7),
                  fontFamily: 'Tajawal',
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                _completeDriverAssignment(order, driverName, driverPhone, true);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: primaryColor,
                foregroundColor: Colors.white,
              ),
              child: const Text(
                'نعم، احفظ',
                style: TextStyle(fontFamily: 'Tajawal'),
              ),
            ),
          ],
        ),
      );
    } else {
      _completeDriverAssignment(order, driverName, driverPhone, false);
    }
  }

  void _completeDriverAssignment(
    Map<String, dynamic> order,
    String driverName,
    String driverPhone,
    bool saveDriver,
  ) {
    // Generate driver ID
    final driverId = 'DRV-${DateTime.now().millisecondsSinceEpoch}';

    // Save driver if requested
    if (saveDriver) {
      final newDriver = {
        'id': driverId,
        'name': driverName,
        'phone': driverPhone,
        'createdAt': DateTime.now().toIso8601String(),
      };

      setState(() {
        _savedDrivers.add(newDriver);
      });
      _saveDrivers();
    }

    // Assign driver to order
    setState(() {
      order['driverId'] = driverId;
      order['driverName'] = driverName;
      order['driverPhone'] = driverPhone;
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'تم تعيين السائق "$driverName" للطلب ${order['id']}',
          style: const TextStyle(fontFamily: 'Tajawal'),
        ),
        backgroundColor: const Color(0xFF00BF63),
      ),
    );
  }



  void _viewPrescriptionImage(String imagePath, Color cardColor, Color textColor) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: cardColor,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Text(
                    'صورة الروشتة',
                    style: TextStyle(
                      color: textColor,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      fontFamily: 'Tajawal',
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: Icon(Icons.close, color: textColor),
                  ),
                ],
              ),
            ),
            Container(
              constraints: BoxConstraints(
                maxHeight: MediaQuery.of(context).size.height * 0.6,
                maxWidth: MediaQuery.of(context).size.width * 0.9,
              ),
              child: imagePath.startsWith('assets/')
                  ? Image.asset(
                      imagePath,
                      fit: BoxFit.contain,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          height: 200,
                          color: Colors.grey[300],
                          child: const Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(Icons.image, color: Colors.grey, size: 48),
                                SizedBox(height: 8),
                                Text(
                                  'صورة الروشتة',
                                  style: TextStyle(fontFamily: 'Tajawal'),
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    )
                  : Image.file(
                      File(imagePath),
                      fit: BoxFit.contain,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          height: 200,
                          color: Colors.grey[300],
                          child: const Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(Icons.error, color: Colors.red, size: 48),
                                SizedBox(height: 8),
                                Text(
                                  'خطأ في تحميل الصورة',
                                  style: TextStyle(fontFamily: 'Tajawal'),
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
            ),
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }
}
