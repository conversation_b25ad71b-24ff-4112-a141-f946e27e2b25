const express = require('express');
const Order = require('../models/Order');
const Medicine = require('../models/Medicine');
const Pharmacy = require('../models/Pharmacy');
const Inventory = require('../models/Inventory');
const { protect, requireVerification, authorize } = require('../middleware/auth');
const { body, validationResult } = require('express-validator');

const router = express.Router();

// @desc    Get user orders
// @route   GET /api/orders
// @access  Private
router.get('/', protect, requireVerification, async (req, res) => {
  try {
    const { status, page = 1, limit = 10 } = req.query;

    const query = { user: req.user.id };
    if (status) query.status = status;

    const skip = (parseInt(page) - 1) * parseInt(limit);

    const orders = await Order.find(query)
      .populate('pharmacy', 'name address phone')
      .populate('items.medicine', 'name manufacturer form strength')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    const total = await Order.countDocuments(query);

    res.json({
      success: true,
      data: {
        orders,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / parseInt(limit))
        }
      }
    });
  } catch (error) {
    console.error('Get orders error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب الطلبات'
    });
  }
});

// @desc    Get medicine requests for pharmacy (by governorate)
// @route   GET /api/orders/pharmacy
// @access  Private/Pharmacy
router.get('/pharmacy', protect, authorize('pharmacy'), async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      status = 'pending'
    } = req.query;

    // Get pharmacy information
    console.log('🔍 req.user:', req.user);
    console.log('🔍 req.user.id:', req.user.id);
    console.log('🔍 req.user.role:', req.user.role);

    // Validate ObjectId
    const mongoose = require('mongoose');
    if (!mongoose.Types.ObjectId.isValid(req.user.id)) {
      console.error('❌ Invalid ObjectId:', req.user.id);
      return res.status(400).json({
        success: false,
        message: 'معرف الصيدلية غير صحيح'
      });
    }

    const Pharmacy = require('../models/Pharmacy');
    const pharmacy = await Pharmacy.findById(req.user.id);

    if (!pharmacy) {
      return res.status(404).json({
        success: false,
        message: 'الصيدلية غير موجودة'
      });
    }

    // Get medicine requests in the same governorate
    const pharmacyGovernorate = pharmacy.address?.governorate || pharmacy.governorate || 'القاهرة';
    console.log(`🏥 صيدلية ${pharmacy.name} في محافظة: ${pharmacyGovernorate}`);

    const query = {
      type: 'medicine_request',
      status: status,
      userGovernorate: pharmacyGovernorate
    };

    console.log('🔍 البحث عن الطلبات بالشروط:', query);

    const orders = await Order.find(query)
      .populate('user', 'name phone email')
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const total = await Order.countDocuments(query);

    console.log(`📋 تم العثور على ${orders.length} طلب من أصل ${total} في المحافظة`);

    // إضافة تفاصيل الطلبات للتشخيص
    orders.forEach((order, index) => {
      console.log(`${index + 1}. طلب ${order.orderNumber}: ${order.medicineRequest?.name} - المحافظة: ${order.userGovernorate}`);
    });

    res.json({
      success: true,
      data: orders,
      pagination: {
        current: parseInt(page),
        pages: Math.ceil(total / limit),
        total
      }
    });
  } catch (error) {
    console.error('Get pharmacy orders error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب الطلبات'
    });
  }
});

// @desc    Get order by ID
// @route   GET /api/orders/:id
// @access  Private
router.get('/:id', protect, async (req, res) => {
  try {
    const order = await Order.findById(req.params.id)
      .populate('user', 'name email phone')
      .populate('pharmacy', 'name address phone services')
      .populate('items.medicine', 'name manufacturer form strength images');

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'الطلب غير موجود'
      });
    }

    // Check if user owns the order or is pharmacy/admin
    if (req.user.role === 'user' && order.user._id.toString() !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'غير مصرح لك بعرض هذا الطلب'
      });
    }

    if (req.user.role === 'pharmacy' && order.pharmacy._id.toString() !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'غير مصرح لك بعرض هذا الطلب'
      });
    }

    res.json({
      success: true,
      data: { order }
    });
  } catch (error) {
    console.error('Get order error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب الطلب'
    });
  }
});

// @desc    Create simple medicine request (for mobile app)
// @route   POST /api/orders
// @access  Private
router.post('/', protect, [
  body('medicineName').notEmpty().withMessage('اسم الدواء مطلوب'),
  body('quantity').isInt({ min: 1 }).withMessage('الكمية يجب أن تكون رقم صحيح أكبر من صفر')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'بيانات غير صحيحة',
        errors: errors.array().map(error => error.msg)
      });
    }

    const {
      medicineName,
      quantity,
      description,
      imageUrl,
      prescriptionImageUrl,
      userGovernorate,
      currency = 'EGP'
    } = req.body;

    // Create a simple medicine request order
    // This will be broadcasted to all verified pharmacies
    const orderNumber = `ORD-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

    // Get user information
    const user = await require('../models/User').findById(req.user.id);
    if (!user) {
      return res.status(400).json({
        success: false,
        message: 'المستخدم غير موجود'
      });
    }

    // Create simple medicine request
    console.log(`📝 إنشاء طلب جديد للمستخدم: ${req.user.id}`);
    console.log(`📋 تفاصيل الطلب: ${medicineName} - الكمية: ${quantity} - المحافظة: ${userGovernorate}`);

    const order = await Order.create({
      orderNumber,
      user: req.user.id,
      status: 'pending',
      type: 'medicine_request',
      medicineRequest: {
        name: medicineName,
        quantity: quantity,
        description: description,
        imageUrl: imageUrl,
        prescriptionImageUrl: prescriptionImageUrl
      },
      contactInfo: {
        phone: user.phone,
        name: user.name
      },
      userGovernorate: userGovernorate || user.governorate || 'القاهرة',
      currency: currency,
      createdAt: new Date(),
      updatedAt: new Date()
    });

    // Populate user information for response
    await order.populate('user', 'name phone email');

    console.log(`✅ تم إنشاء الطلب بنجاح! ID: ${order._id}`);

    res.status(201).json({
      success: true,
      message: 'تم إرسال طلب الدواء بنجاح. ستتلقى عروض من الصيدليات قريباً',
      data: {
        order: {
          id: order._id,
          orderNumber: order.orderNumber,
          medicineName: medicineName,
          quantity: quantity,
          status: order.status,
          createdAt: order.createdAt
        }
      }
    });
  } catch (error) {
    console.error('Create order error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في إنشاء الطلب'
    });
  }
});

// @desc    Create test medicine request (for debugging)
// @route   POST /api/orders/test
// @access  Public (no authentication required)
router.post('/test', async (req, res) => {
  try {
    const orderNumber = `TEST-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

    const order = await Order.create({
      orderNumber,
      user: '507f1f77bcf86cd799439011', // Test user ID
      status: 'pending',
      type: 'medicine_request',
      medicineRequest: {
        name: 'باراسيتامول تجريبي',
        quantity: 2,
        description: 'طلب تجريبي للاختبار'
      },
      contactInfo: {
        phone: '01234567890',
        name: 'مستخدم تجريبي'
      },
      userGovernorate: 'أسيوط', // نفس محافظة الصيدلية المسجلة
      currency: 'EGP',
      createdAt: new Date(),
      updatedAt: new Date()
    });

    res.status(201).json({
      success: true,
      message: 'تم إنشاء طلب تجريبي بنجاح',
      data: {
        order: {
          id: order._id,
          orderNumber: order.orderNumber,
          medicineName: order.medicineRequest.name,
          quantity: order.medicineRequest.quantity,
          status: order.status,
          userGovernorate: order.userGovernorate,
          createdAt: order.createdAt
        }
      }
    });
  } catch (error) {
    console.error('Create test order error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في إنشاء الطلب التجريبي'
    });
  }
});

// @desc    Submit offer for medicine request
// @route   POST /api/orders/:id/offer
// @access  Private/Pharmacy
router.post('/:id/offer', protect, authorize('pharmacy'), [
  body('price').isFloat({ min: 0 }).withMessage('السعر يجب أن يكون رقم موجب'),
  body('availableQuantity').isInt({ min: 1 }).withMessage('الكمية المتاحة يجب أن تكون رقم صحيح موجب'),
  body('notes').optional().isString().withMessage('الملاحظات يجب أن تكون نص')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'بيانات غير صحيحة',
        errors: errors.array().map(error => error.msg)
      });
    }

    const { price, availableQuantity, notes, estimatedTime } = req.body;
    const orderId = req.params.id;

    // Get the medicine request
    const order = await Order.findById(orderId);
    if (!order || order.type !== 'medicine_request') {
      return res.status(404).json({
        success: false,
        message: 'طلب الدواء غير موجود'
      });
    }

    // Get pharmacy information
    const Pharmacy = require('../models/Pharmacy');
    const pharmacy = await Pharmacy.findById(req.user.id);

    if (!pharmacy) {
      return res.status(404).json({
        success: false,
        message: 'الصيدلية غير موجودة'
      });
    }

    // Check if pharmacy already submitted an offer
    const existingOffer = order.offers?.find(offer =>
      offer.pharmacy.toString() === req.user.id
    );

    if (existingOffer) {
      return res.status(400).json({
        success: false,
        message: 'لقد قدمت عرضاً مسبقاً على هذا الطلب'
      });
    }

    // Add offer to the order
    if (!order.offers) {
      order.offers = [];
    }

    order.offers.push({
      pharmacy: req.user.id,
      pharmacyName: pharmacy.name,
      pharmacyPhone: pharmacy.phone,
      pharmacyAddress: pharmacy.address,
      price: price,
      availableQuantity: availableQuantity,
      notes: notes,
      estimatedTime: estimatedTime || 30, // 30 minutes default
      status: 'pending',
      submittedAt: new Date()
    });

    // Update order status to 'offers_received' if it's the first offer
    if (order.offers.length === 1) {
      order.status = 'offers_received';
      console.log(`📈 تم تحديث حالة الطلب إلى: offers_received`);
    }

    await order.save();
    console.log(`✅ تم حفظ العرض بنجاح! إجمالي العروض: ${order.offers.length}`);

    res.status(201).json({
      success: true,
      message: 'تم تقديم العرض بنجاح',
      data: {
        orderId: order._id,
        offerCount: order.offers.length
      }
    });
  } catch (error) {
    console.error('Submit offer error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في تقديم العرض'
    });
  }
});

// @desc    Get offers for user's order
// @route   GET /api/orders/:id/offers
// @access  Private/User
router.get('/:id/offers', protect, async (req, res) => {
  try {
    const orderId = req.params.id;

    // Get the order with offers
    console.log(`🔍 البحث عن الطلب: ${orderId} للمستخدم: ${req.user.id}`);

    const order = await Order.findOne({
      _id: orderId,
      $or: [
        { user: req.user.id },
        { user: null } // للطلبات التجريبية
      ]
    }).populate('offers.pharmacy', 'name phone address');

    console.log(`📋 تم العثور على الطلب: ${order ? 'نعم' : 'لا'}`);
    if (order) {
      console.log(`📊 عدد العروض: ${order.offers?.length || 0}`);
    }

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'الطلب غير موجود'
      });
    }

    console.log(`📤 إرسال البيانات للمستخدم:`);
    console.log(`   - الطلب: ${order.orderNumber}`);
    console.log(`   - العروض: ${order.offers?.length || 0}`);

    res.json({
      success: true,
      data: {
        order: {
          id: order._id,
          orderNumber: order.orderNumber,
          medicineName: order.medicineRequest.name,
          quantity: order.medicineRequest.quantity,
          status: order.status,
          createdAt: order.createdAt
        },
        offers: order.offers || []
      }
    });
  } catch (error) {
    console.error('Get offers error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب العروض'
    });
  }
});

// @desc    Debug endpoint to check order details
// @route   GET /api/orders/:id/debug
// @access  Public (for debugging)
router.get('/:id/debug', async (req, res) => {
  try {
    const orderId = req.params.id;
    const order = await Order.findById(orderId).populate('offers.pharmacy', 'name phone');

    if (!order) {
      return res.json({
        success: false,
        message: 'Order not found'
      });
    }

    res.json({
      success: true,
      data: {
        id: order._id,
        orderNumber: order.orderNumber,
        status: order.status,
        medicineRequest: order.medicineRequest,
        userGovernorate: order.userGovernorate,
        offersCount: order.offers?.length || 0,
        offers: order.offers || [],
        createdAt: order.createdAt
      }
    });
  } catch (error) {
    res.json({
      success: false,
      error: error.message
    });
  }
});

// @desc    Accept pharmacy offer
// @route   PUT /api/orders/:id/offers/:offerId/accept
// @access  Private/User
router.put('/:id/offers/:offerId/accept', protect, async (req, res) => {
  try {
    const { id: orderId, offerId } = req.params;

    // Get the order
    const order = await Order.findOne({
      _id: orderId,
      user: req.user.id
    });

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'الطلب غير موجود'
      });
    }

    // Find the offer
    const offer = order.offers.id(offerId);
    if (!offer) {
      return res.status(404).json({
        success: false,
        message: 'العرض غير موجود'
      });
    }

    // Accept the offer and reject others
    order.offers.forEach(o => {
      if (o._id.toString() === offerId) {
        o.status = 'accepted';
      } else {
        o.status = 'rejected';
      }
    });

    // Update order status and assign pharmacy
    order.status = 'confirmed';
    order.pharmacy = offer.pharmacy;
    order.pricing = {
      subtotal: offer.price,
      total: offer.price
    };

    await order.save();

    res.json({
      success: true,
      message: 'تم قبول العرض بنجاح',
      data: {
        orderId: order._id,
        pharmacyName: offer.pharmacyName,
        price: offer.price
      }
    });
  } catch (error) {
    console.error('Accept offer error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في قبول العرض'
    });
  }
});

// @desc    Update order status (Pharmacy only)
// @route   PUT /api/orders/:id/status
// @access  Private/Pharmacy
router.put('/:id/status', protect, authorize('pharmacy'), [
  body('status').isIn(['confirmed', 'preparing', 'ready', 'out_for_delivery', 'delivered', 'cancelled'])
    .withMessage('حالة الطلب غير صحيحة'),
  body('notes').optional().isString().withMessage('الملاحظات يجب أن تكون نص')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'بيانات غير صحيحة',
        errors: errors.array().map(error => error.msg)
      });
    }

    const { status, notes, driverInfo } = req.body;

    const order = await Order.findById(req.params.id);

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'الطلب غير موجود'
      });
    }

    // Check if pharmacy owns the order
    if (order.pharmacy.toString() !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'غير مصرح لك بتحديث هذا الطلب'
      });
    }

    // Update driver info if provided
    if (driverInfo && status === 'out_for_delivery') {
      order.delivery.driver = driverInfo;
    }

    // Update status
    await order.updateStatus(status, notes, req.user.id, 'Pharmacy');

    // If delivered, update inventory
    if (status === 'delivered') {
      for (const item of order.items) {
        const inventory = await Inventory.findOne({
          pharmacy: req.user.id,
          medicine: item.medicine
        });

        if (inventory) {
          await inventory.addMovement('sale', item.quantity, 'بيع - طلب رقم ' + order.orderNumber, order.orderNumber, req.user.id);
          await inventory.releaseReservedStock(item.quantity);
        }
      }

      order.delivery.actualDeliveryTime = new Date();
    }

    // If cancelled, release reserved stock
    if (status === 'cancelled') {
      for (const item of order.items) {
        const inventory = await Inventory.findOne({
          pharmacy: req.user.id,
          medicine: item.medicine
        });

        if (inventory) {
          await inventory.releaseReservedStock(item.quantity);
        }
      }
    }

    await order.save();

    res.json({
      success: true,
      message: 'تم تحديث حالة الطلب بنجاح',
      data: { order }
    });
  } catch (error) {
    console.error('Update order status error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في تحديث حالة الطلب'
    });
  }
});

// @desc    Cancel order
// @route   PUT /api/orders/:id/cancel
// @access  Private
router.put('/:id/cancel', protect, [
  body('reason').notEmpty().withMessage('سبب الإلغاء مطلوب')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'بيانات غير صحيحة',
        errors: errors.array().map(error => error.msg)
      });
    }

    const { reason } = req.body;

    const order = await Order.findById(req.params.id);

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'الطلب غير موجود'
      });
    }

    // Check permissions
    const canCancel = (req.user.role === 'user' && order.user.toString() === req.user.id) ||
                     (req.user.role === 'pharmacy' && order.pharmacy.toString() === req.user.id) ||
                     req.user.role === 'admin';

    if (!canCancel) {
      return res.status(403).json({
        success: false,
        message: 'غير مصرح لك بإلغاء هذا الطلب'
      });
    }

    // Check if order can be cancelled
    if (['delivered', 'cancelled'].includes(order.status)) {
      return res.status(400).json({
        success: false,
        message: 'لا يمكن إلغاء هذا الطلب'
      });
    }

    // Release reserved stock
    if (req.user.role === 'pharmacy' || req.user.role === 'admin') {
      for (const item of order.items) {
        const inventory = await Inventory.findOne({
          pharmacy: order.pharmacy,
          medicine: item.medicine
        });

        if (inventory) {
          await inventory.releaseReservedStock(item.quantity);
        }
      }
    }

    // Cancel order
    await order.cancelOrder(reason, req.user.id, req.user.role === 'user' ? 'User' : req.user.role === 'pharmacy' ? 'Pharmacy' : 'Admin');

    res.json({
      success: true,
      message: 'تم إلغاء الطلب بنجاح',
      data: { order }
    });
  } catch (error) {
    console.error('Cancel order error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في إلغاء الطلب'
    });
  }
});

// @desc    Rate order (User only)
// @route   PUT /api/orders/:id/rate
// @access  Private/User
router.put('/:id/rate', protect, authorize('user'), [
  body('pharmacyRating').isInt({ min: 1, max: 5 }).withMessage('تقييم الصيدلية يجب أن يكون بين 1 و 5'),
  body('deliveryRating').isInt({ min: 1, max: 5 }).withMessage('تقييم التوصيل يجب أن يكون بين 1 و 5'),
  body('comment').optional().isString().withMessage('التعليق يجب أن يكون نص')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'بيانات غير صحيحة',
        errors: errors.array().map(error => error.msg)
      });
    }

    const { pharmacyRating, deliveryRating, comment } = req.body;

    const order = await Order.findById(req.params.id);

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'الطلب غير موجود'
      });
    }

    // Check if user owns the order
    if (order.user.toString() !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'غير مصرح لك بتقييم هذا الطلب'
      });
    }

    // Check if order is delivered
    if (order.status !== 'delivered') {
      return res.status(400).json({
        success: false,
        message: 'يمكن تقييم الطلب فقط بعد التسليم'
      });
    }

    // Check if already rated
    if (order.rating.ratedAt) {
      return res.status(400).json({
        success: false,
        message: 'تم تقييم هذا الطلب بالفعل'
      });
    }

    // Add rating
    await order.addRating(pharmacyRating, deliveryRating, comment);

    // Update pharmacy rating
    const pharmacy = await Pharmacy.findById(order.pharmacy);
    if (pharmacy) {
      const currentTotal = pharmacy.rating.average * pharmacy.rating.count;
      const newCount = pharmacy.rating.count + 1;
      const newAverage = (currentTotal + pharmacyRating) / newCount;

      pharmacy.rating.average = Math.round(newAverage * 10) / 10;
      pharmacy.rating.count = newCount;
      await pharmacy.save();
    }

    res.json({
      success: true,
      message: 'تم إضافة التقييم بنجاح',
      data: { order }
    });
  } catch (error) {
    console.error('Rate order error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في إضافة التقييم'
    });
  }
});

module.exports = router;
