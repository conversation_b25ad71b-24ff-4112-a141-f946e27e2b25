const mongoose = require('mongoose');

const orderSchema = new mongoose.Schema({
  orderNumber: {
    type: String,
    unique: true,
    required: true
  },
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'المستخدم مطلوب']
  },
  pharmacy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Pharmacy',
    required: false // Not required for medicine requests
  },
  type: {
    type: String,
    enum: ['medicine_request', 'pharmacy_order'],
    default: 'pharmacy_order'
  },
  // For simple medicine requests
  medicineRequest: {
    name: String,
    quantity: Number,
    description: String,
    imageUrl: String,
    prescriptionImageUrl: String
  },
  items: [{
    medicine: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Medicine',
      required: false // Not required for medicine requests
    },
    quantity: {
      type: Number,
      required: false,
      min: [1, 'الكمية يجب أن تكون على الأقل 1']
    },
    unitPrice: {
      type: Number,
      required: false,
      min: [0, 'السعر لا يمكن أن يكون سالب']
    },
    totalPrice: {
      type: Number,
      required: false
    },
    notes: String
  }],
  prescription: {
    hasImage: { type: Boolean, default: false },
    imageUrl: String,
    doctorName: String,
    notes: String
  },
  pricing: {
    subtotal: {
      type: Number,
      required: false,
      min: 0
    },
    deliveryFee: {
      type: Number,
      default: 0,
      min: 0
    },
    discount: {
      amount: { type: Number, default: 0 },
      percentage: { type: Number, default: 0 },
      couponCode: String,
      reason: String
    },
    tax: {
      amount: { type: Number, default: 0 },
      percentage: { type: Number, default: 0 }
    },
    total: {
      type: Number,
      required: false,
      min: 0
    }
  },
  deliveryAddress: {
    street: { type: String, required: false },
    city: { type: String, required: false },
    governorate: { type: String, required: false },
    postalCode: String,
    landmark: String,
    coordinates: {
      latitude: Number,
      longitude: Number
    }
  },
  contactInfo: {
    phone: { type: String, required: false },
    alternativePhone: String,
    email: String,
    name: String
  },
  currency: {
    type: String,
    default: 'EGP'
  },
  userGovernorate: {
    type: String,
    required: false
  },
  // Offers from pharmacies (for medicine requests)
  offers: [{
    pharmacy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Pharmacy',
      required: true
    },
    pharmacyName: String,
    pharmacyPhone: String,
    pharmacyAddress: {
      street: String,
      city: String,
      governorate: String
    },
    price: {
      type: Number,
      required: true,
      min: 0
    },
    availableQuantity: {
      type: Number,
      required: true,
      min: 1
    },
    notes: String,
    estimatedTime: {
      type: Number, // in minutes
      default: 30
    },
    status: {
      type: String,
      enum: ['pending', 'accepted', 'rejected'],
      default: 'pending'
    },
    submittedAt: {
      type: Date,
      default: Date.now
    }
  }],
  status: {
    type: String,
    enum: [
      'pending',        // في الانتظار
      'offers_received', // تم استلام عروض
      'confirmed',      // مؤكد
      'preparing',      // قيد التجهيز
      'ready',          // جاهز للتسليم
      'out_for_delivery', // في الطريق
      'delivered',      // تم التسليم
      'cancelled',      // ملغي
      'returned'        // مرتجع
    ],
    default: 'pending'
  },
  statusHistory: [{
    status: String,
    timestamp: { type: Date, default: Date.now },
    notes: String,
    updatedBy: {
      type: mongoose.Schema.Types.ObjectId,
      refPath: 'statusHistory.updatedByModel'
    },
    updatedByModel: {
      type: String,
      enum: ['User', 'Pharmacy', 'Admin']
    }
  }],
  paymentMethod: {
    type: String,
    enum: ['cash', 'card', 'wallet', 'insurance'],
    required: false // Not required for medicine requests
  },
  paymentStatus: {
    type: String,
    enum: ['pending', 'paid', 'failed', 'refunded'],
    default: 'pending'
  },
  paymentDetails: {
    transactionId: String,
    paymentGateway: String,
    paidAt: Date,
    refundedAt: Date,
    refundAmount: Number
  },
  delivery: {
    type: {
      type: String,
      enum: ['delivery', 'pickup'],
      default: 'delivery'
    },
    driver: {
      name: String,
      phone: String,
      vehicleInfo: String
    },
    estimatedTime: Date,
    actualDeliveryTime: Date,
    deliveryInstructions: String,
    deliveryProof: {
      signature: String,
      photo: String,
      receivedBy: String
    }
  },
  urgency: {
    type: String,
    enum: ['normal', 'urgent', 'emergency'],
    default: 'normal'
  },
  rating: {
    pharmacyRating: { type: Number, min: 1, max: 5 },
    deliveryRating: { type: Number, min: 1, max: 5 },
    overallRating: { type: Number, min: 1, max: 5 },
    comment: String,
    ratedAt: Date
  },
  notes: String,
  internalNotes: String, // ملاحظات داخلية للصيدلية
  cancellation: {
    reason: String,
    cancelledBy: {
      type: mongoose.Schema.Types.ObjectId,
      refPath: 'cancellation.cancelledByModel'
    },
    cancelledByModel: {
      type: String,
      enum: ['User', 'Pharmacy', 'Admin']
    },
    cancelledAt: Date,
    refundAmount: Number
  },
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes
orderSchema.index({ orderNumber: 1 });
orderSchema.index({ user: 1, createdAt: -1 });
orderSchema.index({ pharmacy: 1, createdAt: -1 });
orderSchema.index({ status: 1 });
orderSchema.index({ paymentStatus: 1 });
orderSchema.index({ createdAt: -1 });
orderSchema.index({ 'delivery.estimatedTime': 1 });

// Virtual for order age
orderSchema.virtual('orderAge').get(function() {
  return Date.now() - this.createdAt;
});

// Virtual for estimated delivery time remaining
orderSchema.virtual('timeRemaining').get(function() {
  if (this.delivery.estimatedTime) {
    return this.delivery.estimatedTime - Date.now();
  }
  return null;
});

// Pre-save middleware to generate order number
orderSchema.pre('save', async function(next) {
  if (this.isNew) {
    const count = await this.constructor.countDocuments();
    this.orderNumber = `ORD-${Date.now()}-${(count + 1).toString().padStart(4, '0')}`;
    
    // Add initial status to history
    this.statusHistory.push({
      status: this.status,
      timestamp: new Date(),
      notes: 'تم إنشاء الطلب'
    });
  }
  next();
});

// Pre-save middleware to calculate totals
orderSchema.pre('save', function(next) {
  // Calculate item totals
  this.items.forEach(item => {
    item.totalPrice = item.quantity * item.unitPrice;
  });
  
  // Calculate subtotal
  this.pricing.subtotal = this.items.reduce((sum, item) => sum + item.totalPrice, 0);
  
  // Calculate discount amount
  if (this.pricing.discount.percentage > 0) {
    this.pricing.discount.amount = this.pricing.subtotal * (this.pricing.discount.percentage / 100);
  }
  
  // Calculate tax amount
  if (this.pricing.tax.percentage > 0) {
    const taxableAmount = this.pricing.subtotal - this.pricing.discount.amount;
    this.pricing.tax.amount = taxableAmount * (this.pricing.tax.percentage / 100);
  }
  
  // Calculate total
  this.pricing.total = this.pricing.subtotal 
    + this.pricing.deliveryFee 
    + this.pricing.tax.amount 
    - this.pricing.discount.amount;
  
  next();
});

// Instance method to update status
orderSchema.methods.updateStatus = function(newStatus, notes, updatedBy, updatedByModel) {
  this.status = newStatus;
  this.statusHistory.push({
    status: newStatus,
    timestamp: new Date(),
    notes: notes,
    updatedBy: updatedBy,
    updatedByModel: updatedByModel
  });
  
  return this.save();
};

// Instance method to cancel order
orderSchema.methods.cancelOrder = function(reason, cancelledBy, cancelledByModel, refundAmount = 0) {
  this.status = 'cancelled';
  this.cancellation = {
    reason: reason,
    cancelledBy: cancelledBy,
    cancelledByModel: cancelledByModel,
    cancelledAt: new Date(),
    refundAmount: refundAmount
  };
  
  this.statusHistory.push({
    status: 'cancelled',
    timestamp: new Date(),
    notes: `تم إلغاء الطلب: ${reason}`,
    updatedBy: cancelledBy,
    updatedByModel: cancelledByModel
  });
  
  return this.save();
};

// Instance method to add rating
orderSchema.methods.addRating = function(pharmacyRating, deliveryRating, comment) {
  this.rating = {
    pharmacyRating: pharmacyRating,
    deliveryRating: deliveryRating,
    overallRating: Math.round((pharmacyRating + deliveryRating) / 2),
    comment: comment,
    ratedAt: new Date()
  };
  
  return this.save();
};

// Static method to get order statistics
orderSchema.statics.getStatistics = function(pharmacyId = null) {
  const matchQuery = pharmacyId ? { pharmacy: pharmacyId } : {};
  
  return this.aggregate([
    { $match: matchQuery },
    {
      $group: {
        _id: null,
        totalOrders: { $sum: 1 },
        pendingOrders: {
          $sum: { $cond: [{ $eq: ['$status', 'pending'] }, 1, 0] }
        },
        confirmedOrders: {
          $sum: { $cond: [{ $eq: ['$status', 'confirmed'] }, 1, 0] }
        },
        deliveredOrders: {
          $sum: { $cond: [{ $eq: ['$status', 'delivered'] }, 1, 0] }
        },
        cancelledOrders: {
          $sum: { $cond: [{ $eq: ['$status', 'cancelled'] }, 1, 0] }
        },
        totalRevenue: { $sum: '$pricing.total' },
        averageOrderValue: { $avg: '$pricing.total' },
        averageRating: { $avg: '$rating.overallRating' }
      }
    }
  ]);
};

// Static method to get orders by date range
orderSchema.statics.getOrdersByDateRange = function(startDate, endDate, pharmacyId = null) {
  const matchQuery = {
    createdAt: { $gte: startDate, $lte: endDate }
  };
  
  if (pharmacyId) {
    matchQuery.pharmacy = pharmacyId;
  }
  
  return this.find(matchQuery)
    .populate('user', 'name email phone')
    .populate('pharmacy', 'name address.governorate')
    .populate('items.medicine', 'name manufacturer')
    .sort({ createdAt: -1 });
};

// Static method to get popular medicines
orderSchema.statics.getPopularMedicines = function(limit = 10, pharmacyId = null) {
  const matchQuery = pharmacyId ? { pharmacy: pharmacyId } : {};
  
  return this.aggregate([
    { $match: matchQuery },
    { $unwind: '$items' },
    {
      $group: {
        _id: '$items.medicine',
        totalQuantity: { $sum: '$items.quantity' },
        totalOrders: { $sum: 1 },
        totalRevenue: { $sum: '$items.totalPrice' }
      }
    },
    { $sort: { totalQuantity: -1 } },
    { $limit: limit },
    {
      $lookup: {
        from: 'medicines',
        localField: '_id',
        foreignField: '_id',
        as: 'medicine'
      }
    },
    { $unwind: '$medicine' }
  ]);
};

module.exports = mongoose.model('Order', orderSchema);
